import argparse

_ARGS = None

def get_args():
    return _ARGS

def set_args(args):
    global _ARGS
    _ARGS = args


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('--epochs', type=int, default=50)
    parser.add_argument('--batch-size', type=int, default=128)
    parser.add_argument('--kfold', type=int, default=5)
    parser.add_argument('--test-fold', type=int, default=3, help='which part is used for validation, range from 0 to kfold - 1')
    parser.add_argument('--lr', type=float, default=1e-5)
    parser.add_argument('--weight-decay', type=float, default=0.01)
    parser.add_argument('--embed-dim', type=int, default=64)
    parser.add_argument('--epsilon-power', type=float, default=1e-1)
    parser.add_argument('--epsilon-area', type=float, default=1e-1)
    parser.add_argument('--nlayers', type=int, default=4)
    parser.add_argument('--nheads', type=int, default=4)
    parser.add_argument('--model-name', type=str, default='mlp', choices=['mlp', 'unet', 'transformer', 'xgboost'])

    # XGBoost specific parameters
    parser.add_argument('--xgb-n-estimators', type=int, default=100, help='Number of boosting rounds for XGBoost')
    parser.add_argument('--xgb-max-depth', type=int, default=6, help='Maximum depth of XGBoost trees')
    parser.add_argument('--xgb-learning-rate', type=float, default=0.1, help='Learning rate for XGBoost')
    parser.add_argument('--xgb-subsample', type=float, default=1.0, help='Subsample ratio for XGBoost')
    parser.add_argument('--xgb-colsample-bytree', type=float, default=1.0, help='Column subsample ratio for XGBoost')
    parser.add_argument('--xgb-reg-alpha', type=float, default=0.0, help='L1 regularization for XGBoost')
    parser.add_argument('--xgb-reg-lambda', type=float, default=1.0, help='L2 regularization for XGBoost')
    args = parser.parse_args()
    set_args(args)

    return args
