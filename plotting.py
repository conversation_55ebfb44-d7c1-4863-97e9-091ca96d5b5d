import matplotlib.pyplot as plt
import numpy as np


def plot_losses(train_losses, test_losses, nepochs, args):

    plt.figure()

    # Handle XGBoost case where we only have single values
    if args.model_name == 'xgboost':
        # For XGBoost, just show the final loss as a bar chart
        categories = ['Train Loss', 'Test Loss']
        values = [train_losses[0], test_losses[0]]
        plt.bar(categories, values)
        plt.title(f'XGBoost Final Losses, {args.test_fold} / {args.kfold} part data as test')
        plt.ylabel('Loss value')
    else:
        # For other models, show loss curves over epochs
        x = np.arange(nepochs)
        plt.plot(x, train_losses, label='train loss')
        plt.plot(x, test_losses, label=f'test loss')
        plt.title(f'Training and Testing Losses, {args.test_fold} / {args.kfold} part data as test')
        plt.xlabel('Epoch Idx')
        plt.ylabel('Loss value')
        plt.legend()

    plt.savefig(f'./plots/losses_plot_{args.model_name}_{args.test_fold}.png')


def plot_test_acc(test_acc, nepochs, args):

    plt.figure()

    # Handle XGBoost case where we only have single values
    if args.model_name == 'xgboost':
        # For XGBoost, just show the final accuracy as a bar chart
        categories = ['Power Accuracy', 'Area Accuracy']
        values = [test_acc[0][0], test_acc[1][0]]
        plt.bar(categories, values)
        plt.title(f'XGBoost Final Accuracy, deviating {args.epsilon_area *100 }%')
        plt.ylabel('Accuracy')
        plt.ylim(0, 1)  # Accuracy is between 0 and 1
    else:
        # For other models, show accuracy curves over epochs
        x = np.arange(nepochs)
        plt.plot(x, test_acc[0], label='power acc')
        plt.plot(x, test_acc[1], label=f'area acc')
        plt.title(f'Testing acc, deviating {args.epsilon_area *100 }%')
        plt.xlabel('Epoch Idx')
        plt.ylabel('Acc')
        plt.legend()

    plt.savefig(f'./plots/acc_plot_{args.model_name}_{args.test_fold}.png')